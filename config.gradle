ext {

    android = [
            compileSdkVersion: 34,
            applicationId    : "com.jd.amr.hmi",
            minSdkVersion    : 23,
            targetSdkVersion : 34,
            versionCode      : 10006,
            versionName      : "1.00.06",
    ]
    key_store = [
            "KEYSTORE_FILE": ".../keystore/jd-car.keystore",
            "KEYSTORE_PWD" : "android",
            "KEY_PWD"      : "android",
            "KEY_ALIAS"    : "keyAlias"

    ]
    //version配置
//    versions = [
//            "support-version": "28.0.3",
//            "junit-version"  : "4.12",
//    ]
    javacompile = [
            javaSourceCompatibility: JavaVersion.VERSION_1_8,
            javaTargetCompatibility: JavaVersion.VERSION_1_8
    ]


    //依赖第三方配置
    dependencies = [
            "hutool"                         :'cn.hutool:hutool-all:5.8.5',
            "mmkv"                           : "com.tencent:mmkv-static:1.2.1",
            "protobuf-java3"                 : "com.google.protobuf:protobuf-javalite:3.18.1",
            "protoc3"                        : "com.google.protobuf:protoc:3.18.1",
            "constraintlayout"               : "androidx.constraintlayout:constraintlayout:1.1.3",
            "zxing"                          : "com.google.zxing:core:3.3.1",
            "eventbus"                       : "org.greenrobot:eventbus:3.2.0",
            "butterknife-compiler"           : "com.jakewharton:butterknife-compiler:10.2.3",
            "butterknife"                    : "com.jakewharton:butterknife:10.2.3",
            "recyclerview"                   : "androidx.recyclerview:recyclerview:1.1.0",
            "xlog"                           : "com.elvishew:xlog:1.10.1",
            "rxjava3"                        : "io.reactivex.rxjava3:rxjava:3.0.0-RC2",
            "rxandroid3"                     : "io.reactivex.rxjava3:rxandroid:3.0.0",
            "rxbinding"                      : "com.jakewharton.rxbinding4:rxbinding:4.0.0",
            "rxbinding-core"                 : "com.jakewharton.rxbinding4:rxbinding-core:4.0.0",
            "retrofit2"                      : "com.squareup.retrofit2:retrofit:2.9.0",
            "retrofit2-adapter"              : "com.squareup.retrofit2:adapter-rxjava3:2.9.0",
            "gson"                           : "com.google.code.gson:gson:2.8.6",
            "mqtt-android"                   : "org.eclipse.paho:org.eclipse.paho.android.service:1.1.1",
            "mqtt-service"                   : "org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.1.0",
            "jackson-databind"               : "com.fasterxml.jackson.core:jackson-databind:2.9.7",
            "jackson-annotations"            : "com.fasterxml.jackson.core:jackson-annotations:2.9.7",
            "jackson-module-jaxb-annotations": "com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.9.7",
    ]
}