package com.jd.amr.hmi.ui.activity;

import android.app.Activity;
import android.content.Intent;
import android.os.Build;
import android.widget.TextView;

import androidx.annotation.RequiresApi;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.iscas.rcljava.entity.topic.VehicleInfo;
import com.jd.amr.hmi.Amr;
import com.jd.amr.hmi.BuildConfig;
import com.jd.amr.hmi.R;
import com.jd.amr.hmi.common.Constants;
import com.jd.amr.hmi.manager.GlobalRobotDataManager;
import com.jd.amr.hmi.manager.http.impl.model.TaskDataModel;
import com.jd.amr.hmi.manager.http.impl.model.TokenDataModel;
import com.jd.amr.hmi.model.data.event.TaskInfoStartEvent;
import com.jd.amr.hmi.model.data.event.VehicleInfoEvent;
import com.jd.amr.hmi.model.data.http.ShelfRequestBody;
import com.jd.amr.hmi.mqtt.JdMqttService;
import com.jd.amr.hmi.util.ServiceUtils;
import com.jd.ugv.pad.common.base.BaseCallBack;
import com.jd.ugv.pad.common.base.BaseResult;
import com.jd.ugv.pad.common.base.TokenData;
import com.jd.ugv.pad.common.base.TokenInfoData;
import com.jd.ugv.pad.common.utils.LogUtils;
import com.jd.ugv.pad.common.utils.StringUtils;

import org.greenrobot.eventbus.Subscribe;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;


public class InitActivity extends BaseActivity {

    private static final String TAG = "InitActivity";

    private ConstraintLayout mInitWholeCl;
    private Intent mJdMqttService;

    private TokenDataModel mTokenDataModel;

    @Override
    protected Activity getActivity() {
        return this;
    }

    @Override
    protected int layoutId() {
        return R.layout.activity_init;
    }

    @Override
    protected void initView() {
        mInitWholeCl = findViewById(R.id.init_whole_cl);
    }

    @Override
    protected void initListener() {
        mInitWholeCl.setOnClickListener(v -> {
//           enterActivity(PickOwhsActivity.class);
            LogUtils.i(TAG, "点击重启");
            exec("reboot");
        });
    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    @Override
    protected void initData() {
//        test();
       if(!hasToken()){
           getTokenInfo();
       }
    }

    private void test(){
        VehicleInfo vehicleInfo = new VehicleInfo();
        vehicleInfo.robot_vin = "XMR110004";
        vehicleInfo.client_passwd = "070c33718bc74e8d";
        GlobalRobotDataManager.getInstance().setVehicleInfo(vehicleInfo);
        mRobotNameTv.setText(StringUtils.getLastCharactersEnhanced(vehicleInfo.robot_vin, 6));
        startMqttService();
    }


    @Subscribe
    public void onVehicleInfoEvent(VehicleInfoEvent event) {
        if(event != null) {
            LogUtils.i(TAG, "onVehicleInfoEvent===>event: " + event.toString());
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    String robotName = event.getVehicleInfo().robot_plate;
                    if(robotName == null){
                        robotName = event.getVehicleInfo().robot_vin;
                        if(robotName != null){
                            //获取车架号后6位
                            robotName = StringUtils.getLastCharactersEnhanced(robotName, 6);
                        }
                    }
                    mRobotNameTv.setText(robotName);
                    //注册mqtt服务
                    if (ServiceUtils.isServiceRunning(Amr.getInstance(), JdMqttService.class.getName())) {
                        return;
                    }
                    startMqttService();
                    //获取token
                    getTokenInfo();
                }
            });
        }
    }

    @Subscribe
    public void onTaskStartEvent(TaskInfoStartEvent event){
        if(event != null){
            //进入行走页面
            enterActivity(MovingActivity.class);
        }
    }



    private void startMqttService(){
        mJdMqttService = new Intent(this, JdMqttService.class);
        mJdMqttService.setAction(JdMqttService.ACTION_START_INIT);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(mJdMqttService);
        } else {
            startService(mJdMqttService);
        }
    }

    private void getTokenInfo(){
        if(mTokenDataModel == null){
            mTokenDataModel = new TokenDataModel();
        }
        String robotName = GlobalRobotDataManager.getInstance().getVehicleName();
        String apiKey = GlobalRobotDataManager.getInstance().getApiKey();
        LogUtils.i(TAG, "getTokenInfo===>robotName: " + robotName + ", apiKey: " + apiKey);
        if(robotName != null && apiKey != null){
            mTokenDataModel.getTokenInfo(robotName, apiKey, new BaseCallBack() {
                @Override
                public void success(BaseResult baseResult) {
                    TokenInfoData tokenInfoData = (TokenInfoData) baseResult.getData();
                    if (tokenInfoData != null) {
                        TokenData tokenData = new TokenData(tokenInfoData.getAccessToken(), robotName, tokenInfoData.getExpiresIn(), BuildConfig.LOP_DN);
                        tokenData.setVehicleName(robotName);
                        tokenData.setApkKey(apiKey);
                        tokenData.setUrl(BuildConfig.PROXY_URL + Constants.TOKEN);
                        GlobalRobotDataManager.getInstance().setTokenData(tokenData);
                    }
                }

                @Override
                public boolean failOrError(BaseResult baseResult, String e) {
                    LogUtils.e(TAG, "getTokenInfo failOrError: " + e);
                    return false;
                }
            });
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        LogUtils.i(TAG, "=========InitActivity onDestroy=========");
        if (mJdMqttService != null) {
            stopService(mJdMqttService);
        }
        if(mRobotRemoteService != null){
            stopService(mRobotRemoteService);
        }
    }

    private String exec(String command) {
        Process process = null;
        BufferedReader reader = null;
        InputStreamReader is = null;
        DataOutputStream os = null;
        try {
            process = Runtime.getRuntime().exec("su");
            is = new InputStreamReader(process.getInputStream());
            reader = new BufferedReader(is);
            os = new DataOutputStream(process.getOutputStream());
            os.writeBytes(command + "\n");
            os.writeBytes("exit\n");
            os.flush();
            int read;
            char[] buffer = new char[4096];
            StringBuilder output = new StringBuilder();
            while ((read = reader.read(buffer)) > 0) {
                output.append(buffer, 0, read);
            }
            process.waitFor();
            return output.toString();
        } catch (IOException | InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
                if (is != null) {
                    is.close();
                }
                if (reader != null) {
                    reader.close();
                }
                if (process != null) {
                    process.destroy();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}