package com.jd.amr.hmi.mqtt;

import android.text.TextUtils;

import com.jd.amr.hmi.mqtt.handler.CmdAlarmUpdateHandler;
import com.jd.amr.hmi.mqtt.handler.CmdFollowUserHandler;
import com.jd.amr.hmi.mqtt.handler.CmdWaitNextTaskHandler;
import com.jd.amr.hmi.mqtt.handler.CmdRebootAndroidHandler;
import com.jd.amr.hmi.mqtt.handler.ICmdHandler;

import java.util.concurrent.ConcurrentHashMap;

/**
 * @descrition: 远程指令工厂类
 * @author: douyanghui
 * @date: 2024/8/14 19:35
 */
public class CmdHandlerFactory {

    private static volatile CmdHandlerFactory instance;

    private ConcurrentHashMap<String, ICmdHandler> mCmdHandlerMap = new ConcurrentHashMap<>();

    private CmdHandlerFactory(){
        init();
    }

    private void init(){
        ICmdHandler followUserHandler = new CmdFollowUserHandler();
        mCmdHandlerMap.put(followUserHandler.cmdType(), followUserHandler);

        ICmdHandler alarmUpdateHandler = new CmdAlarmUpdateHandler();
        mCmdHandlerMap.put(alarmUpdateHandler.cmdType(), alarmUpdateHandler);

        ICmdHandler pickTaskCompleteHandler = new CmdWaitNextTaskHandler();
        mCmdHandlerMap.put(pickTaskCompleteHandler.cmdType(), pickTaskCompleteHandler);

        ICmdHandler rebootAndroidHandler = new CmdRebootAndroidHandler();
        mCmdHandlerMap.put(rebootAndroidHandler.cmdType(), rebootAndroidHandler);
    }

    public static CmdHandlerFactory getInstance(){
        if(instance == null){
            synchronized (CmdHandlerFactory.class){
                if(instance == null){
                    instance = new CmdHandlerFactory();
                }
            }
        }
        return instance;
    }

    public ICmdHandler getHandler(String cmdType){
        if(mCmdHandlerMap == null || TextUtils.isEmpty(cmdType)){
            return null;
        }
        return mCmdHandlerMap.get(cmdType);
    }
}
