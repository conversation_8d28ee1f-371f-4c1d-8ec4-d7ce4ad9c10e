package com.jd.amr.hmi.model.enums;

public enum RemoteCmdType {

    CMD_REMOTE_CALL("EVENT_OPERATION_REMOTE_CALL", "上报远程呼叫设备"),

    CMD_OPERATION_FOLLOW("CMD_OPERATION_FOLLOW", "监控同步HMI跟进中"),

    CMD_ALARM_UPDATE("CMD_ALARM_UPDATE", "同步告警状态"),

    CMD_WAIT_NEXT_TASK("CMD_WAIT_NEXT_TASK", "等待下一个任务下发"),

    CMD_REMOTE_HMI_BOOT("CMD_REMOTE_HMI_BOOT", "重启安卓");


    private String type;

    private String name;

    RemoteCmdType(String type,  String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }
}
