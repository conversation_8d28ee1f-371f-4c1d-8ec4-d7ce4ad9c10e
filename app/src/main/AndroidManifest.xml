<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.jd.amr.hmi"
    android:sharedUserId="android.uid.system">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <application
        android:name=".Amr"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <activity
            android:name=".ui.activity.BaseActivity"
            android:exported="false"
            android:theme="@style/Theme.notAnimation" />
        <activity
            android:name=".ui.activity.InitActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/Theme.notAnimation">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.activity.TestActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/Theme.notAnimation">
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.MAIN" />-->

<!--                <category android:name="android.intent.category.LAUNCHER" />-->
<!--            </intent-filter>-->
        </activity>
        <activity android:name=".ui.activity.PickOwhsActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/Theme.notAnimation">
        </activity>
        <activity android:name=".ui.activity.ExceptionActivity"
            android:launchMode="singleTask"
            android:theme="@style/Theme.notAnimation"/>
        <activity android:name=".ui.activity.MovingActivity"
            android:launchMode="singleTask"
            android:theme="@style/Theme.notAnimation"/>
        <service
            android:name=".mqtt.JdMqttService"
            android:enabled="true"
            android:exported="true" />
        <service android:name=".manager.socket.RobotRemoteService" />
        <service android:name="org.eclipse.paho.android.service.MqttService" />
    </application>

</manifest>